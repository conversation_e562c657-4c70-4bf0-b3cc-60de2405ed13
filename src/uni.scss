/* 颜色变量 */

/* 行为相关颜色 */
// $uni-color-primary: #007aff;
// $uni-color-success: #4cd964;
// $uni-color-warning: #f0ad4e;
// $uni-color-error: #dd524d;

// /* 文字基本颜色 */
// $uni-text-color: #333; // 基本色
// $uni-text-color-inverse: #fff; // 反色
// $uni-text-color-grey: #999; // 辅助灰色，如加载更多的提示信息
// $uni-text-color-placeholder: #808080;
// $uni-text-color-disable: #c0c0c0;

// /* 背景颜色 */
// $uni-bg-color: #fff;
// $uni-bg-color-grey: #f8f8f8;
// $uni-bg-color-hover: #f1f1f1; // 点击状态颜色
// $uni-bg-color-mask: rgba(0, 0, 0, 0.4); // 遮罩颜色

// /* 边框颜色 */
// $uni-border-color: #c8c7cc;

// /* 尺寸变量 */

// /* 文字尺寸 */
// $uni-font-size-sm: 12px;
// $uni-font-size-base: 14px;
// $uni-font-size-lg: 16;

// /* 图片尺寸 */
// $uni-img-size-sm: 20px;
// $uni-img-size-base: 26px;
// $uni-img-size-lg: 40px;

// /* Border Radius */
// $uni-border-radius-sm: 2px;
// $uni-border-radius-base: 3px;
// $uni-border-radius-lg: 6px;
// $uni-border-radius-circle: 50%;

// /* 水平间距 */
// $uni-spacing-row-sm: 5px;
// $uni-spacing-row-base: 10px;
// $uni-spacing-row-lg: 15px;

// /* 垂直间距 */
// $uni-spacing-col-sm: 4px;
// $uni-spacing-col-base: 8px;
// $uni-spacing-col-lg: 12px;

// /* 透明度 */
// $uni-opacity-disabled: 0.3; // 组件禁用态的透明度

// /* 文章场景相关 */
// $uni-color-title: #2c405a; // 文章标题颜色
// $uni-font-size-title: 20px;
// $uni-color-subtitle: #555; // 二级标题颜色
// $uni-font-size-subtitle: 18px;
// $uni-color-paragraph: #3f536e; // 文章段落颜色
// $uni-font-size-paragraph: 15px;

/* 颜色变量 */

// $primary: #007aff;
// $success: #4cd964;
// $warning: #f0ad4e;
// $error: #dd524d;

$text-red: #ff0000;

$primary-50: #fff9ec;
$primary-100: #fff1d3;
$primary-200: #ffdfa5;
$primary-300: #ffc66d;
$primary-400: #ffa232;
$primary-500: #ff860a;
$primary-600: #ff6d00;
$primary-700: #cc4e02;
$primary-800: #a13d0b;
$primary-900: #82340c;
$primary-950: #461804;

$primary: $primary-600;
$vitality-orange: #f29421; // 活力橙色

$text-main: #333;
$text-info: #666666;
$text-inverse: #fff;
$text-grey: #999;
$text-placeholder: #808080;
$text-disable: #c0c0c0;

$border-base: #c8c7cc;
$border-radius-sm: 2px;
$border-radius-base: 3px;
$border-radius-lg: 6px;
$border-radius-circle: 50%;

$spacing-4: 8rpx;
$spacing-6: 12rpx;
$spacing-8: 16rpx;
$spacing-10: 20rpx;
$spacing-12: 24rpx;
$spacing-14: 28rpx;
$spacing-16: 32rpx;
$spacing-18: 36rpx;
$spacing-20: 40rpx;
$spacing-24: 48rpx;

$bg-color: #f5f5f5;
$bg-hover: #f1f1f1;
$bg-mask: rgba(0, 0, 0, 0.4);

view,
text,
image {
    box-sizing: border-box;
}

uni-button:after,
button:after {
    border: none;
}

::v-deep .uni-nav-bar-text {
    font-size: 34rpx !important;
    font-weight: 500 !important;
    color: #000;
}

::v-deep .uni-icons {
    font-size: 50rpx !important;
}
.safe-area-inset-bottom {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
}

.page,
uni-page-wrapper,
page {
    box-sizing: border-box;
    background: #f5f5f5;
    color: #333333;
}

.color-base {
    color: #121212;
}

.color-main {
    color: $text-main;
}

.color-info {
    color: $text-info;
}

.color-grey {
    color: $text-grey;
}

.color-placeholder {
    color: $text-placeholder;
}
