<template>
  <view class="container">
    <!-- 房源列表 -->
    <scroll-view
      scroll-y
      enable-flex
      class="house-list"
      @scrolltolower="loadMore"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
    >
      <!-- 搜索栏 -->
      <view class="search-section">
        <view class="search-bar" @tap="handleSearch">
          <text class="i-carbon-search text-gray-400 text-32rpx"></text>
          <text class="search-placeholder">搜索楼盘名称、位置</text>
        </view>
      </view>

      <!-- 筛选组件 -->
      <view class="filter-container">
        <FilterPanel @filter-change="handleFilterChange" />
      </view>

      <view
        class="house-item"
        v-for="house in houseList"
        :key="house.id"
        @tap="navigateToDetail(house.id)"
      >
        <!-- 房源图片 -->
        <view class="house-image-container">
          <image
            :src="house.image"
            mode="aspectFill"
            class="house-image"
            :lazy-load="true"
          />

          <!-- 状态标签 -->
          <view
            v-if="house.status"
            class="status-tag"
            :class="getStatusClass(house.status)"
          >
            {{ house.status }}
          </view>

          <!-- 特价标签 -->
          <view v-if="house.specialOffer" class="special-tag">
            <text>{{ house.specialOffer }}</text>
          </view>
        </view>

        <!-- 房源信息 -->
        <view class="house-info">
          <!-- 楼盘名称和房屋类型 -->
          <view class="house-title">
            <text class="title-text">{{ house.title }}</text>
            <view class="house-type-tag">住宅</view>
          </view>

          <!-- 价格信息 -->
          <view class="price-section">
            <text class="price-value">{{ house.price }}</text>
          </view>

          <!-- 位置信息 -->
          <view class="location-info">
            <text class="location-text">{{ house.location }}</text>
          </view>

          <!-- 标签区域 -->
          <view v-if="house.tags && house.tags.length" class="tags-section">
            <text v-for="tag in house.tags" :key="tag" class="tag">
              {{ tag }}
            </text>
          </view>

          <!-- 关注人数 -->
          <view v-if="house.attentionCount" class="attention-info">
            <text class="attention-text"
              >近期有{{ house.attentionCount }}人关注该楼盘</text
            >
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="loading" class="loading-more">
        <text class="loading-text">加载中...</text>
      </view>

      <view v-if="!hasMore && houseList.length > 0" class="no-more">
        <text>没有更多了</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import type { NewHouseItem } from "@/types/house";
import CustomNavBar from "@/components/CustomNavBar.vue";
import FilterPanel from "./components/FilterPanel.vue";

// 响应式数据
const currentLocation = ref("北京");
const isRefreshing = ref(false);
const loading = ref(false);
const hasMore = ref(true);
const showFilterModal = ref(false);

// 筛选条件
const currentFilters = reactive({
  area: "",
  price: "",
  houseType: "",
  features: [] as string[],
  developer: "",
  decoration: "",
});

// 选中的筛选条件
const selectedFilters = reactive({
  price: "",
  houseType: "",
  features: [] as string[],
});

// 保存原始房源列表
const originalHouseList = [
  {
    id: "1",
    image: "https://picsum.photos/seed/house-1/300/200",
    title: "元垄",
    location: "石景山·石景山其它/建面120-196㎡/3,4居",
    price: "75000元/平",
    originalPrice: "78000元/平",
    specialOffer: "有15套特价房",
    buildTypes: ["3居", "4居"],
    tags: ["近地铁", "综合商场", "公园"],
    openTime: "",
    attentionCount: 29,
    hasVR: false,
    status: "在售",
  },
  {
    id: "2",
    image: "https://picsum.photos/seed/house-2/300/200",
    title: "招商云璟境",
    location: "通州·梨园/建面79-128㎡/3,4居",
    price: "60000元/平",
    originalPrice: "63000元/平",
    specialOffer: "有9套特价房",
    buildTypes: ["2居", "3居"],
    tags: ["近地铁", "医疗配套", "期房"],
    openTime: "",
    attentionCount: 33,
    hasVR: true,
    status: "在售",
  },
  {
    id: "3",
    image: "https://picsum.photos/seed/house-3/300/200",
    title: "京投发展森与天成",
    location: "丰台·新宫/建面55-148㎡/1,2,3,4居",
    price: "78000元/平",
    originalPrice: "80000元/平",
    specialOffer: "",
    buildTypes: ["1居", "2居", "3居", "4居"],
    tags: ["近地铁", "公园", "期房"],
    openTime: "",
    attentionCount: 0,
    hasVR: true,
    status: "在售",
  },
  {
    id: "4",
    image: "https://picsum.photos/seed/house-4/300/200",
    title: "清樾府",
    location: "昌平·沙河/建面86-143㎡/3,4居",
    price: "46000元/平",
    originalPrice: "48000元/平",
    specialOffer: "",
    buildTypes: ["2居", "3居", "4居"],
    tags: ["期房", "小三居", "低密居所"],
    openTime: "",
    attentionCount: 39,
    hasVR: false,
    status: "在售",
  },
  {
    id: "5",
    image: "https://picsum.photos/seed/house-5/300/200",
    title: "地铁10号线350米 高楼层 精装修",
    location: "2室1厅 | 81.93㎡ | 西南",
    price: "349万",
    originalPrice: "42,598元/平",
    specialOffer: "",
    buildTypes: ["2居"],
    tags: ["满五", "小高层", "户型方正"],
    openTime: "",
    attentionCount: 0,
    hasVR: false,
    status: "二手房",
  },
];

// 房源列表数据
const houseList = ref<NewHouseItem[]>([...originalHouseList]);

// 方法
const handleSearch = () => {
  uni.showToast({
    title: "搜索功能开发中",
    icon: "none",
  });
};

const selectLocation = () => {
  uni.showActionSheet({
    itemList: ["北京", "上海", "广州", "深圳", "杭州"],
    success: (res) => {
      const locations = ["北京", "上海", "广州", "深圳", "杭州"];
      currentLocation.value = locations[res.tapIndex];
    },
  });
};

const toggleFilter = (index: number) => {
  if (index === 1) {
    // 价格筛选
    showFilterModal.value = true;
  } else {
    uni.showToast({
      title: "筛选功能开发中",
      icon: "none",
    });
  }
};

const getStatusClass = (status: string) => {
  switch (status) {
    case "在售":
      return "status-selling";
    case "热销":
      return "status-hot";
    case "即将开盘":
      return "status-coming";
    case "售罄":
      return "status-sold";
    case "二手房":
      return "status-second";
    default:
      return "status-default";
  }
};

const navigateToDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/newHouse/detail?id=${id}`,
  });
};

const loadMore = () => {
  if (loading.value || !hasMore.value) return;

  loading.value = true;

  // 模拟加载更多数据
  setTimeout(() => {
    const newHouses: NewHouseItem[] = [
      {
        id: `${Date.now()}`,
        title: "保利和光尘樾",
        image: "https://picsum.photos/seed/house-6/300/200",
        location: "海淀区 · 西二旗",
        price: "68000元/平",
        priceRange: "550-720万",
        status: "在售",
        buildTypes: ["高层"],
        tags: ["地铁房", "科技园区"],
        hasVR: false,
        openTime: "现房销售",
        developer: "保利地产",
        propertyType: "住宅",
        propertyYears: 70,
      },
    ];

    houseList.value.push(...newHouses);
    loading.value = false;

    // 模拟没有更多数据
    if (houseList.value.length >= 10) {
      hasMore.value = false;
    }
  }, 1000);
};

const onRefresh = () => {
  isRefreshing.value = true;

  setTimeout(() => {
    // 重新加载数据
    houseList.value = houseList.value.slice(0, 3);
    hasMore.value = true;
    isRefreshing.value = false;

    uni.showToast({
      title: "刷新成功",
      icon: "success",
    });
  }, 1000);
};

// 处理筛选变化
const handleFilterChange = (filters: any) => {
  Object.assign(currentFilters, filters);
  console.log("筛选条件已更新:", currentFilters);

  // 这里可以根据筛选条件过滤房源列表
  // 实际项目中应该调用API重新获取数据
  filterHouseList();
};

// 根据筛选条件过滤房源列表
const filterHouseList = () => {
  // 模拟筛选逻辑
  let filteredList = [...originalHouseList];

  // 根据价格筛选
  if (currentFilters.price) {
    const [min, max] = currentFilters.price.split("-").map(Number);
    filteredList = filteredList.filter((house) => {
      const price = parseInt(house.price.replace(/[^\d]/g, ""));
      if (max) {
        return price >= min && price <= max;
      } else {
        return price >= min;
      }
    });
  }

  // 根据房型筛选
  if (currentFilters.houseType) {
    filteredList = filteredList.filter((house) => {
      return house.buildTypes.some((type) =>
        type.includes(currentFilters.houseType)
      );
    });
  }

  // 根据特色筛选
  if (currentFilters.features.length > 0) {
    filteredList = filteredList.filter((house) => {
      return currentFilters.features.some((feature) => {
        if (feature === "video") return house.hasVR;
        if (feature === "subway") return house.tags.includes("近地铁");
        if (feature === "school") return house.tags.includes("学区");
        return false;
      });
    });
  }

  houseList.value = filteredList;
};

onMounted(() => {
  // 页面加载时的初始化操作
});
</script>

<style lang="scss" scoped>
.container {
  .filter-container {
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: #fff;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  }

  /* 搜索栏样式 */
  .search-section {
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx;
    background-color: #fff;
    gap: 24rpx;
  }

  .search-bar {
    flex: 1;
    display: flex;
    align-items: center;
    height: 72rpx;
    padding: 0 24rpx;
    background-color: #f5f5f5;
    border-radius: 36rpx;
    gap: 16rpx;
  }

  .search-placeholder {
    color: #999;
    font-size: 28rpx;
  }

  .location-btn {
    display: flex;
    align-items: center;
    gap: 8rpx;
  }

  .location-text {
    color: #333;
    font-size: 28rpx;
    font-weight: 500;
  }

  /* 房源列表样式 */
  .house-list {
    flex: 1;
    padding: 24rpx 32rpx;
    box-sizing: border-box;
    height: calc(100vh - 180rpx);
  }

  .house-item {
    background-color: #fff;
    border-radius: 16rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  }

  .house-image-container {
    position: relative;
    height: 360rpx;
  }

  .house-image {
    width: 100%;
    height: 100%;
  }

  .status-tag {
    position: absolute;
    top: 16rpx;
    left: 16rpx;
    padding: 6rpx 16rpx;
    border-radius: 4rpx;
    color: #fff;
    font-size: 24rpx;
    font-weight: 500;
  }

  .status-selling {
    background-color: #3388ff;
  }

  .status-hot {
    background-color: #ff4d4f;
  }

  .status-coming {
    background-color: #52c41a;
  }

  .status-sold {
    background-color: #999;
  }

  .status-second {
    background-color: #2f88ff;
  }

  .special-tag {
    position: absolute;
    top: 16rpx;
    right: 16rpx;
    padding: 6rpx 16rpx;
    background-color: #fff5e6;
    border-radius: 4rpx;
    color: #ff8c00;
    font-size: 22rpx;
  }

  .house-info {
    padding: 24rpx;
  }

  .house-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12rpx;
  }

  .title-text {
    color: #333;
    font-size: 32rpx;
    font-weight: 600;
    flex: 1;
  }

  .house-type-tag {
    padding: 4rpx 12rpx;
    background-color: #f0f0f0;
    border-radius: 4rpx;
    color: #666;
    font-size: 22rpx;
  }

  .location-info {
    margin-bottom: 16rpx;
  }

  .location-text {
    color: #666;
    font-size: 26rpx;
    line-height: 1.4;
  }

  .price-section {
    margin-bottom: 16rpx;
  }

  .price-value {
    color: #ff4d4f;
    font-size: 32rpx;
    font-weight: 600;
  }

  .tags-section {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
    margin-bottom: 16rpx;
  }

  .tag {
    padding: 4rpx 12rpx;
    background-color: #f5f5f5;
    border-radius: 4rpx;
    color: #666;
    font-size: 22rpx;
  }

  .attention-info {
    margin-top: 12rpx;
  }

  .attention-text {
    color: #ff4d4f;
    font-size: 24rpx;
  }

  /* 加载状态样式 */
  .loading-more {
    display: flex;
    justify-content: center;
    padding: 40rpx;
  }

  .loading-text {
    color: #999;
    font-size: 28rpx;
  }

  .no-more {
    display: flex;
    justify-content: center;
    padding: 40rpx;
    color: #999;
    font-size: 28rpx;
  }
}
</style>
