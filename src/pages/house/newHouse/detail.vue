<template>
  <view class="detail-page">
    <!-- 自定义导航栏 -->
    <CustomNavBar title="" :showBack="true" :isImmersive="true" />
    
    <!-- 房源图片轮播 -->
    <swiper class="house-swiper" indicator-dots circular autoplay :indicator-active-color="'#3388ff'">
      <swiper-item v-for="(image, index) in houseImages" :key="index">
        <image :src="image" mode="aspectFill" class="swiper-image" />
      </swiper-item>
    </swiper>
    
    <!-- 房源基本信息 -->
    <view class="house-info">
      <view class="house-header">
        <view class="title-section">
          <text class="house-title">{{ houseDetail.title }}</text>
          <view class="house-tags">
            <text v-if="houseDetail.hasVR" class="vr-tag">VR看房</text>
            <text class="status-tag" :class="getStatusClass(houseDetail.status)">{{ houseDetail.status }}</text>
          </view>
        </view>
        <view class="favorite-btn" @tap="toggleFavorite">
          <text class="i-carbon-heart text-24px" :class="{ 'favorited': isFavorited }"></text>
        </view>
      </view>
      
      <view class="price-section">
        <view class="price-main">
          <text class="current-price">{{ houseDetail.price }}</text>
        </view>
        <text v-if="houseDetail.originalPrice" class="original-price">参考总价 {{ houseDetail.priceRange }}</text>
      </view>
      
      <view v-if="houseDetail.specialOffer" class="special-offer">
        <text class="offer-text">{{ houseDetail.specialOffer }}</text>
      </view>
      
      <view class="location-info">
        <text class="i-carbon-location text-16px location-icon"></text>
        <text class="location-text">{{ houseDetail.location }}</text>
      </view>
      
      <view class="build-types">
        <text class="types-label">户型：</text>
        <text v-for="(type, index) in houseDetail.buildTypes" :key="type" class="build-type">
          {{ type }}<text v-if="index < houseDetail.buildTypes.length - 1">、</text>
        </text>
      </view>
      
      <view class="tags-section">
        <text v-for="tag in houseDetail.tags" :key="tag" class="tag">{{ tag }}</text>
      </view>
    </view>
    
    <!-- 基础信息 -->
    <view class="info-section">
      <view class="section-header">
        <text class="section-title">楼盘详情</text>
      </view>
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">开发商</text>
          <text class="info-value">{{ houseDetail.developer || '暂无数据' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">物业类型</text>
          <text class="info-value">{{ houseDetail.propertyType || '暂无数据' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">产权年限</text>
          <text class="info-value">{{ houseDetail.propertyYears ? houseDetail.propertyYears + '年' : '暂无数据' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">装修状况</text>
          <text class="info-value">{{ houseDetail.decoration || '暂无数据' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">开盘时间</text>
          <text class="info-value">{{ houseDetail.openTime || '暂无数据' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">交房时间</text>
          <text class="info-value">{{ houseDetail.deliveryTime || '暂无数据' }}</text>
        </view>
      </view>
    </view>
    
    <!-- 小区特点 -->
    <view class="info-section">
      <view class="section-header">
        <text class="section-title">小区特点</text>
      </view>
      <view class="features-grid">
        <view class="feature-item">
          <text class="i-carbon-checkmark text-18px feature-icon"></text>
          <text class="feature-text">封闭小区</text>
        </view>
        <view class="feature-item">
          <text class="i-carbon-checkmark text-18px feature-icon"></text>
          <text class="feature-text">人车分流</text>
        </view>
        <view class="feature-item">
          <text class="i-carbon-checkmark text-18px feature-icon"></text>
          <text class="feature-text">绿化率：30%</text>
        </view>
        <view class="feature-item">
          <text class="i-carbon-checkmark text-18px feature-icon"></text>
          <text class="feature-text">容积率：1.04</text>
        </view>
        <view class="feature-item">
          <text class="i-carbon-checkmark text-18px feature-icon"></text>
          <text class="feature-text">车位比：1:1.1</text>
        </view>
        <view class="feature-item">
          <text class="i-carbon-checkmark text-18px feature-icon"></text>
          <text class="feature-text">物业费：7.38元/㎡/月</text>
        </view>
      </view>
    </view>
    
    <!-- 销售信息 -->
    <view class="info-section">
      <view class="section-header">
        <text class="section-title">销售信息</text>
      </view>
      <view class="sales-info">
        <view class="sales-item">
          <text class="sales-label">售楼处地址</text>
          <text class="sales-value">{{ houseDetail.salesAddress || '暂无数据' }}</text>
        </view>
        <view class="sales-item">
          <text class="sales-label">售楼处电话</text>
          <text class="sales-value phone-number" @tap="callPhone">{{ houseDetail.salesPhone || '暂无数据' }}</text>
        </view>
        <view class="sales-item">
          <text class="sales-label">营业时间</text>
          <text class="sales-value">{{ houseDetail.businessHours || '暂无数据' }}</text>
        </view>
      </view>
    </view>
    
    <!-- 户型信息 -->
    <view class="info-section">
      <view class="section-header">
        <text class="section-title">户型信息</text>
      </view>
      <view class="layout-filter">
        <view 
          v-for="(filter, index) in layoutFilters" 
          :key="index" 
          class="filter-chip"
          :class="{ active: activeLayoutFilter === index }"
          @tap="switchLayoutFilter(index)">
          <text>{{ filter }}</text>
        </view>
      </view>
      <view class="layout-list">
        <view v-for="layout in houseDetail.layouts" :key="layout.id" class="layout-item">
          <image :src="layout.image" mode="aspectFill" class="layout-image" />
          <view class="layout-info">
            <text class="layout-type">{{ layout.type }}</text>
            <text class="layout-area">{{ layout.area }}㎡</text>
            <text class="layout-price">{{ layout.price }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 周边配套 -->
    <view class="info-section">
      <view class="section-header">
        <text class="section-title">周边配套</text>
      </view>
      <view class="facility-tabs">
        <view 
          v-for="(facility, index) in houseDetail.facilities" 
          :key="facility.type" 
          class="facility-tab"
          :class="{ active: activeFacilityTab === index }"
          @tap="switchFacilityTab(index)">
          <text>{{ facility.type }}</text>
        </view>
      </view>
      <view class="facility-content" v-if="houseDetail.facilities.length > 0">
        <view class="facility-list">
          <text 
            v-for="item in houseDetail.facilities[activeFacilityTab].items" 
            :key="item" 
            class="facility-item">
            {{ item }}
          </text>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-btn favorite-btn" @tap="toggleFavorite">
        <text class="i-carbon-favorite text-24px" :class="{ 'favorited': isFavorited }"></text>
        <text class="btn-text">关注</text>
      </view>
      <button class="action-btn phone-btn" @tap="callPhone">
        <text class="btn-text">电话咨询</text>
      </button>
      <button class="action-btn consult-btn" @tap="consultNow">
        <text class="btn-text">在线咨询</text>
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import CustomNavBar from '@/components/CustomNavBar.vue'

const houseId = ref()
onLoad((options: any) => {
  console.log(options)
  houseId.value = options.id
})

// 收藏状态
const isFavorited = ref(false)

// 户型筛选
const layoutFilters = ['全部(17)', '一居(1)', '二居(1)', '三居(12)']
const activeLayoutFilter = ref(0)
const switchLayoutFilter = (index: number) => {
  activeLayoutFilter.value = index
}

// 周边配套筛选
const activeFacilityTab = ref(0)
const switchFacilityTab = (index: number) => {
  activeFacilityTab.value = index
}

// 房源详情数据
const houseDetail = ref<any>({
  id: '1',
  title: '京投发展森与天成',
  image: 'https://picsum.photos/seed/house-1/300/200',
  location: '丰台·新宫/建面55-148㎡/1,2,3,4居',
  price: '78000元/平',
  originalPrice: '80000元/平',
  priceRange: '380-1250万/套',
  specialOffer: '线下优惠可咨询顾问',
  buildTypes: ['1居', '2居', '3居'],
  tags: ['近地铁', '公园', '期房', '大三居'],
  openTime: '2023-10-28',
  attentionCount: 29,
  hasVR: true,
  status: '在售',
  developer: '北京京投隆德置业有限公司',
  propertyType: '住宅',
  propertyYears: 70,
  decoration: '毛坯',
  deliveryTime: '2025年12月',
  salesAddress: '丰台区新宫地铁站附近',
  salesPhone: '************',
  businessHours: '09:00-18:00',
  layouts: [
    {
      id: 1,
      type: '三居室',
      area: '120',
      price: '900万起',
      image: 'https://picsum.photos/seed/layout-1/200/150'
    },
    {
      id: 2,
      type: '两居室',
      area: '86',
      price: '680万起',
      image: 'https://picsum.photos/seed/layout-2/200/150'
    },
    {
      id: 3,
      type: '四居室',
      area: '156',
      price: '1170万起',
      image: 'https://picsum.photos/seed/layout-3/200/150'
    },
    {
      id: 4,
      type: '一居室',
      area: '55',
      price: '450万起',
      image: 'https://picsum.photos/seed/layout-4/200/150'
    }
  ],
  facilities: [
    {
      type: '交通',
      items: ['地铁1号线', '公交站', '高速入口']
    },
    {
      type: '教育',
      items: ['石景山实验小学', '九中分校', '北京理工大学']
    },
    {
      type: '医疗',
      items: ['石景山医院', '社区卫生服务中心']
    },
    {
      type: '商业',
      items: ['万达广场', '华联超市', '银行网点']
    }
  ]
})

// 房源图片
const houseImages = ref([
  'https://picsum.photos/seed/house-detail-1/400/300',
  'https://picsum.photos/seed/house-detail-2/400/300',
  'https://picsum.photos/seed/house-detail-3/400/300',
  'https://picsum.photos/seed/house-detail-4/400/300'
])

// 方法
const getStatusClass = (status: string) => {
  const statusMap: { [key: string]: string } = {
    '在售': 'on-sale',
    '待售': 'pending',
    '售罄': 'sold-out'
  }
  return statusMap[status] || 'on-sale'
}

const toggleFavorite = () => {
  isFavorited.value = !isFavorited.value
  uni.showToast({
    title: isFavorited.value ? '已关注' : '已取消关注',
    icon: 'none'
  })
}

const consultNow = () => {
  uni.showToast({
    title: '咨询功能开发中',
    icon: 'none'
  })
}

const callPhone = () => {
  uni.makePhoneCall({
    phoneNumber: houseDetail.value.salesPhone
  })
}

// 根据ID加载房源详情
const loadHouseDetail = (id: string) => {
  // 这里应该调用API获取房源详情
  console.log('加载房源详情:', id)
  // 模拟数据已在上面定义
}

onMounted(() => {
  loadHouseDetail(houseId.value)
})
</script>

<style lang="scss">
.detail-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 轮播图样式 */
.house-swiper {
  width: 100%;
  height: 500rpx;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

/* 房源基本信息样式 */
.house-info {
  background-color: #fff;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.house-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.title-section {
  flex: 1;
}

.house-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.house-tags {
  display: flex;
  gap: 12rpx;
  margin-top: 8rpx;
}

.vr-tag {
  background-color: #e6f0ff;
  color: #3388ff;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 22rpx;
}

.status-tag {
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 22rpx;
  color: #fff;
}

.status-tag.on-sale {
  background-color: #3388ff;
}

.status-tag.pending {
  background-color: #ff9500;
}

.status-tag.sold-out {
  background-color: #999;
}

.favorite-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.i-carbon-heart {
  color: #ccc;
}

.i-carbon-heart.favorited {
  color: #ff4d4f;
}

.price-section {
  margin-bottom: 16rpx;
}

.price-main {
  display: flex;
  align-items: baseline;
  margin-bottom: 8rpx;
}

.current-price {
  font-size: 36rpx;
  font-weight: 700;
  color: #ff4d4f;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

.special-offer {
  margin-bottom: 16rpx;
}

.offer-text {
  color: #ff4d4f;
  font-size: 24rpx;
}

.location-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.location-icon {
  color: #999;
  margin-right: 8rpx;
  margin-top: 4rpx;
}

.location-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
  flex: 1;
}

.build-types {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.types-label {
  font-size: 28rpx;
  color: #666;
}

.build-type {
  font-size: 28rpx;
  color: #333;
}

.tags-section {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag {
  background-color: #f5f5f5;
  color: #666;
  padding: 6rpx 16rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
}

/* 信息部分通用样式 */
.info-section {
  background-color: #fff;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 24rpx;
  background-color: #3388ff;
  border-radius: 3rpx;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-label {
  font-size: 26rpx;
  color: #999;
}

.info-value {
  font-size: 28rpx;
  color: #333;
}

/* 小区特点样式 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.feature-icon {
  color: #3388ff;
}

.feature-text {
  font-size: 26rpx;
  color: #666;
}

/* 销售信息样式 */
.sales-info {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.sales-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sales-label {
  font-size: 26rpx;
  color: #999;
}

.sales-value {
  font-size: 26rpx;
  color: #333;
}

.phone-number {
  color: #3388ff;
}

/* 户型信息样式 */
.layout-filter {
  display: flex;
  gap: 16rpx;
  overflow-x: auto;
  white-space: nowrap;
  margin-bottom: 24rpx;
}

.filter-chip {
  padding: 8rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 100rpx;
  font-size: 26rpx;
  color: #666;
}

.filter-chip.active {
  background-color: #e6f0ff;
  color: #3388ff;
}

.layout-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.layout-item {
  display: flex;
  gap: 24rpx;
  padding: 24rpx;
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.layout-image {
  width: 200rpx;
  height: 160rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

.layout-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  justify-content: center;
}

.layout-type {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.layout-area {
  font-size: 26rpx;
  color: #666;
}

.layout-price {
  font-size: 30rpx;
  font-weight: 700;
  color: #ff4d4f;
}

/* 周边配套样式 */
.facility-tabs {
  display: flex;
  margin-bottom: 24rpx;
  overflow-x: auto;
  white-space: nowrap;
  border-bottom: 1rpx solid #eee;
}

.facility-tab {
  padding: 12rpx 24rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.facility-tab.active {
  color: #3388ff;
  font-weight: 500;
}

.facility-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #3388ff;
  border-radius: 2rpx;
}

.facility-content {
  padding: 16rpx 0;
}

.facility-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.facility-item {
  background-color: #f5f5f5;
  color: #666;
  padding: 12rpx 24rpx;
  border-radius: 4rpx;
  font-size: 26rpx;
}

/* 底部操作栏样式 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 16rpx 32rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  gap: 24rpx;
  z-index: 100;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
}

.favorite-btn {
  display: flex;
  flex-direction: column;
  width: 120rpx;
  height: 80rpx;
  background-color: transparent;
}

.favorited {
  color: #ff4d4f;
}

.phone-btn {
  flex: 1;
  background-color: #fff;
  color: #3388ff;
  border: 1rpx solid #3388ff;
}

.consult-btn {
  flex: 1;
  background-color: #3388ff;
  color: #fff;
  border: none;
}

.btn-text {
  font-size: 28rpx;
}
</style>